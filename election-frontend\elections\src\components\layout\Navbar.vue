<template>
  <nav class="navbar">
    <div class="nav-container">
      <ul class="nav-menu">
        <li class="nav-item">
          <a href="#" @click="navigateTo('home')" class="nav-link">Home</a>
        </li>
        <li class="nav-item">
          <a href="#" @click="navigateTo('policies')" class="nav-link">Policy's</a>
        </li>
        <li class="nav-item">
          <a href="#" @click="navigateTo('kaart')" class="nav-link">Kaart</a>
        </li>
        <li class="nav-item">
          <a href="#" @click="navigateTo('partijen')" class="nav-link">Partijen</a>
        </li>
        <li class="nav-item">
          <a href="#" @click="navigateTo('mijn-vragen')" class="nav-link">Mi<PERSON> vragen</a>
        </li>
      </ul>
    </div>
  </nav>
</template>

<script setup>
const emit = defineEmits(['navigate'])

const navigateTo = (page) => {
  emit('navigate', page)
}
</script>

<style scoped>
.navbar {
  background-color: #007bff;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
