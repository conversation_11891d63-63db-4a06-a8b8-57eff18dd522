<script setup>
import { ref } from 'vue'
import Navbar from './components/layout/Navbar.vue'
import Footer from './components/layout/Footer.vue'
import Policies from './views/Policies.vue'
import Kaart from './views/Kaart.vue'
import Partijen from './views/Partijen.vue'
import MijnVragen from './views/MijnVragen.vue'

const currentPage = ref('home')

const handleNavigation = (page) => {
  currentPage.value = page
}
</script>

<template>
  <div id="app">
    <!-- Home pagina -->
    <div v-if="currentPage === 'home'" class="page">
      <Navbar @navigate="handleNavigation" />
      <main class="main-content">
        <h1>Welkom bij Home</h1>
      </main>
      <Footer />
    </div>

    <!-- Andere pagina's -->
    <Policies v-else-if="currentPage === 'policies'" @navigate="handleNavigation" />
    <Kaart v-else-if="currentPage === 'kaart'" @navigate="handleNavigation" />
    <Partijen v-else-if="currentPage === 'partijen'" @navigate="handleNavigation" />
    <MijnVragen v-else-if="currentPage === 'mijn-vragen'" @navigate="handleNavigation" />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
}

.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 2rem;
}
</style>
